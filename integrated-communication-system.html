<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一通信指挥平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://webapi.amap.com/maps?v=2.0&key=93925fbd1712a50696d9921f6498d5c3"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #1a1a1a;
            color: #fff;
            overflow: hidden;
        }

        .main-container {
            display: flex;
            height: 100vh;
        }

        /* Left Sidebar */
        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            border-right: 2px solid #3498db;
            display: flex;
            flex-direction: column;
        }

        .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #34495e;
        }

        .logo h1 {
            color: #3498db;
            font-size: 18px;
            margin-bottom: 5px;
        }

        .logo p {
            color: #bdc3c7;
            font-size: 12px;
        }

        .nav-menu {
            flex: 1;
            padding: 20px 0;
        }

        .nav-item {
            padding: 15px 25px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
            display: flex;
            align-items: center;
        }

        .nav-item:hover, .nav-item.active {
            background: rgba(52, 152, 219, 0.1);
            border-left-color: #3498db;
        }

        .nav-item i {
            margin-right: 12px;
            width: 20px;
            color: #3498db;
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Top Header */
        .header {
            height: 60px;
            background: linear-gradient(90deg, #2c3e50 0%, #3498db 100%);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            border-bottom: 2px solid #3498db;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .header-title {
            font-size: 20px;
            font-weight: bold;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #27ae60;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* Content Panels */
        .content-area {
            flex: 1;
            display: flex;
            position: relative;
        }

        .panel {
            display: none;
            width: 100%;
            height: 100%;
        }

        .panel.active {
            display: flex;
        }

        /* Dashboard Panel */
        .dashboard-panel {
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 20px;
        }

        .dashboard-card {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #3498db;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-header i {
            font-size: 24px;
            color: #3498db;
            margin-right: 10px;
        }

        .card-title {
            font-size: 16px;
            font-weight: bold;
        }

        .card-content {
            height: calc(100% - 50px);
            overflow: hidden;
        }

        /* Map Container */
        #mapContainer {
            width: 100%;
            height: 100%;
            border-radius: 8px;
            position: relative;
        }

        /* Device List */
        .device-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .device-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin-bottom: 8px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 5px;
            border-left: 3px solid #27ae60;
        }

        .device-item.offline {
            border-left-color: #e74c3c;
        }

        .device-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 10px;
        }

        .device-item.offline .device-status {
            background: #e74c3c;
        }

        /* Statistics */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            height: 100%;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 8px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #bdc3c7;
        }

        /* Communication Panel */
        .communication-panel {
            display: grid;
            grid-template-columns: 300px 1fr 300px;
            height: 100%;
        }

        .contacts-section, .chat-section {
            background: #2c3e50;
            border-right: 1px solid #34495e;
        }

        .video-section {
            background: #1a1a1a;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .section-header {
            padding: 15px 20px;
            background: #34495e;
            border-bottom: 1px solid #3498db;
            font-weight: bold;
        }

        .contacts-list, .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 12px;
            margin-bottom: 5px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .contact-item:hover {
            background: rgba(52, 152, 219, 0.1);
        }

        .contact-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-weight: bold;
        }

        .contact-info h4 {
            font-size: 14px;
            margin-bottom: 2px;
        }

        .contact-info p {
            font-size: 12px;
            color: #bdc3c7;
        }

        /* Video Call Interface */
        .video-main {
            width: 80%;
            height: 60%;
            background: #000;
            border-radius: 10px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .video-placeholder {
            text-align: center;
            color: #bdc3c7;
        }

        .video-controls {
            display: flex;
            gap: 15px;
        }

        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s;
        }

        .control-btn.mic {
            background: #27ae60;
            color: white;
        }

        .control-btn.camera {
            background: #3498db;
            color: white;
        }

        .control-btn.hangup {
            background: #e74c3c;
            color: white;
        }

        .control-btn:hover {
            transform: scale(1.1);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .sidebar {
                width: 60px;
            }
            
            .nav-item span {
                display: none;
            }
            
            .logo h1, .logo p {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .communication-panel {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr auto;
            }
            
            .contacts-section, .chat-section {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Left Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <h1><i class="fas fa-satellite-dish"></i> 统一通信</h1>
                <p>Integrated Communication Platform</p>
            </div>
            <nav class="nav-menu">
                <div class="nav-item active" data-panel="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>控制台</span>
                </div>
                <div class="nav-item" data-panel="communication">
                    <i class="fas fa-video"></i>
                    <span>视频会议</span>
                </div>
                <div class="nav-item" data-panel="messaging">
                    <i class="fas fa-comments"></i>
                    <span>即时通讯</span>
                </div>
                <div class="nav-item" data-panel="voice">
                    <i class="fas fa-phone"></i>
                    <span>语音通话</span>
                </div>
                <div class="nav-item" data-panel="files">
                    <i class="fas fa-folder"></i>
                    <span>文件协作</span>
                </div>
                <div class="nav-item" data-panel="contacts">
                    <i class="fas fa-address-book"></i>
                    <span>通讯录</span>
                </div>
                <div class="nav-item" data-panel="map">
                    <i class="fas fa-map"></i>
                    <span>GIS地图</span>
                </div>
                <div class="nav-item" data-panel="devices">
                    <i class="fas fa-cogs"></i>
                    <span>设备管理</span>
                </div>
                <div class="nav-item" data-panel="schedule">
                    <i class="fas fa-calendar"></i>
                    <span>调度管理</span>
                </div>
                <div class="nav-item" data-panel="command">
                    <i class="fas fa-shield-alt"></i>
                    <span>指挥中心</span>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <h2 class="header-title">统一通信指挥平台</h2>
                </div>
                <div class="header-right">
                    <div class="status-indicator">
                        <div class="status-dot"></div>
                        <span>系统正常</span>
                    </div>
                    <div class="status-indicator">
                        <i class="fas fa-users"></i>
                        <span>在线: 156</span>
                    </div>
                    <div class="status-indicator">
                        <i class="fas fa-clock"></i>
                        <span id="currentTime"></span>
                    </div>
                </div>
            </header>

            <!-- Content Area -->
            <div class="content-area">
                <!-- Dashboard Panel -->
                <div class="panel dashboard-panel active" id="dashboard">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <i class="fas fa-map-marked-alt"></i>
                            <h3 class="card-title">设备分布地图</h3>
                        </div>
                        <div class="card-content">
                            <div id="mapContainer"></div>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="card-header">
                            <i class="fas fa-list"></i>
                            <h3 class="card-title">在线设备</h3>
                        </div>
                        <div class="card-content">
                            <div class="device-list" id="deviceList"></div>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="card-header">
                            <i class="fas fa-chart-bar"></i>
                            <h3 class="card-title">系统统计</h3>
                        </div>
                        <div class="card-content">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-number">156</div>
                                    <div class="stat-label">在线设备</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">23</div>
                                    <div class="stat-label">活跃会议</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">89</div>
                                    <div class="stat-label">通话中</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">1.2TB</div>
                                    <div class="stat-label">数据传输</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="card-header">
                            <i class="fas fa-bell"></i>
                            <h3 class="card-title">系统通知</h3>
                        </div>
                        <div class="card-content">
                            <div class="device-list">
                                <div class="device-item">
                                    <div class="device-status"></div>
                                    <div>
                                        <div>紧急调度任务已下发</div>
                                        <small style="color: #bdc3c7;">2分钟前</small>
                                    </div>
                                </div>
                                <div class="device-item">
                                    <div class="device-status"></div>
                                    <div>
                                        <div>设备001离线告警</div>
                                        <small style="color: #bdc3c7;">5分钟前</small>
                                    </div>
                                </div>
                                <div class="device-item">
                                    <div class="device-status"></div>
                                    <div>
                                        <div>会议室A预约成功</div>
                                        <small style="color: #bdc3c7;">10分钟前</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Communication Panel -->
                <div class="panel communication-panel" id="communication">
                    <div class="contacts-section">
                        <div class="section-header">
                            <i class="fas fa-users"></i> 参会人员 (12/100)
                        </div>
                        <div class="contacts-list" id="participantsList"></div>
                    </div>

                    <div class="video-section">
                        <div class="video-main">
                            <div class="video-placeholder">
                                <i class="fas fa-video" style="font-size: 48px; margin-bottom: 20px; color: #3498db;"></i>
                                <h3>多方视频会议</h3>
                                <p>支持最多100人同时在线，1080P高清画质</p>
                            </div>
                        </div>
                        <div class="video-controls">
                            <button class="control-btn mic" title="麦克风">
                                <i class="fas fa-microphone"></i>
                            </button>
                            <button class="control-btn camera" title="摄像头">
                                <i class="fas fa-video"></i>
                            </button>
                            <button class="control-btn" style="background: #f39c12;" title="屏幕共享">
                                <i class="fas fa-desktop"></i>
                            </button>
                            <button class="control-btn hangup" title="挂断">
                                <i class="fas fa-phone-slash"></i>
                            </button>
                        </div>
                    </div>

                    <div class="chat-section">
                        <div class="section-header">
                            <i class="fas fa-comment"></i> 会议聊天
                        </div>
                        <div class="chat-messages" id="chatMessages"></div>
                        <div style="padding: 15px; border-top: 1px solid #34495e;">
                            <div style="display: flex; gap: 10px;">
                                <input type="text" placeholder="输入消息..." style="flex: 1; padding: 8px; border: 1px solid #34495e; border-radius: 4px; background: #2c3e50; color: white;">
                                <button style="padding: 8px 15px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Messaging Panel -->
                <div class="panel" id="messaging" style="display: grid; grid-template-columns: 300px 1fr; height: 100%;">
                    <div class="contacts-section">
                        <div class="section-header">
                            <i class="fas fa-comments"></i> 即时通讯
                        </div>
                        <div style="padding: 15px;">
                            <input type="text" placeholder="搜索联系人..." style="width: 100%; padding: 8px; border: 1px solid #34495e; border-radius: 4px; background: #2c3e50; color: white; margin-bottom: 15px;">
                        </div>
                        <div class="contacts-list" id="messagingContacts"></div>
                    </div>
                    <div style="background: #2c3e50; display: flex; flex-direction: column;">
                        <div class="section-header">
                            <i class="fas fa-user"></i> 张三 - 在线
                            <div style="float: right;">
                                <button style="background: none; border: none; color: #3498db; margin-left: 10px; cursor: pointer;"><i class="fas fa-phone"></i></button>
                                <button style="background: none; border: none; color: #3498db; margin-left: 10px; cursor: pointer;"><i class="fas fa-video"></i></button>
                            </div>
                        </div>
                        <div style="flex: 1; padding: 20px; overflow-y: auto;" id="messagingChat"></div>
                        <div style="padding: 15px; border-top: 1px solid #34495e;">
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <button style="background: none; border: none; color: #3498db; cursor: pointer;"><i class="fas fa-paperclip"></i></button>
                                <button style="background: none; border: none; color: #3498db; cursor: pointer;"><i class="fas fa-image"></i></button>
                                <input type="text" placeholder="输入消息..." style="flex: 1; padding: 8px; border: 1px solid #34495e; border-radius: 4px; background: #34495e; color: white;">
                                <button style="padding: 8px 15px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Voice Call Panel -->
                <div class="panel" id="voice" style="padding: 20px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; height: 100%;">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-phone"></i>
                                <h3 class="card-title">语音通话</h3>
                            </div>
                            <div class="card-content" style="text-align: center; padding: 40px;">
                                <div style="width: 120px; height: 120px; border-radius: 50%; background: linear-gradient(45deg, #3498db, #2ecc71); margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; font-size: 48px;">
                                    <i class="fas fa-user"></i>
                                </div>
                                <h3>张三</h3>
                                <p style="color: #bdc3c7; margin: 10px 0;">通话中 - 00:05:23</p>
                                <div style="display: flex; justify-content: center; gap: 20px; margin-top: 30px;">
                                    <button class="control-btn mic"><i class="fas fa-microphone"></i></button>
                                    <button class="control-btn" style="background: #f39c12;"><i class="fas fa-volume-up"></i></button>
                                    <button class="control-btn hangup"><i class="fas fa-phone-slash"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-history"></i>
                                <h3 class="card-title">通话记录</h3>
                            </div>
                            <div class="card-content">
                                <div class="device-list" id="callHistory"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Files Panel -->
                <div class="panel" id="files" style="padding: 20px;">
                    <div style="display: grid; grid-template-columns: 250px 1fr; gap: 20px; height: 100%;">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-folder-tree"></i>
                                <h3 class="card-title">文件夹</h3>
                            </div>
                            <div class="card-content">
                                <div style="padding: 10px;">
                                    <div style="padding: 8px; cursor: pointer; border-radius: 4px; margin-bottom: 5px; background: rgba(52, 152, 219, 0.1);">
                                        <i class="fas fa-folder" style="color: #f39c12; margin-right: 8px;"></i>会议资料
                                    </div>
                                    <div style="padding: 8px; cursor: pointer; border-radius: 4px; margin-bottom: 5px;">
                                        <i class="fas fa-folder" style="color: #f39c12; margin-right: 8px;"></i>项目文档
                                    </div>
                                    <div style="padding: 8px; cursor: pointer; border-radius: 4px; margin-bottom: 5px;">
                                        <i class="fas fa-folder" style="color: #f39c12; margin-right: 8px;"></i>培训材料
                                    </div>
                                    <div style="padding: 8px; cursor: pointer; border-radius: 4px; margin-bottom: 5px;">
                                        <i class="fas fa-folder" style="color: #f39c12; margin-right: 8px;"></i>系统备份
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-file"></i>
                                <h3 class="card-title">文件列表</h3>
                                <div style="margin-left: auto;">
                                    <button style="background: #3498db; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; margin-left: 10px;">
                                        <i class="fas fa-upload"></i> 上传
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 15px; padding: 10px;" id="filesList"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contacts Panel -->
                <div class="panel" id="contacts" style="padding: 20px;">
                    <div style="display: grid; grid-template-columns: 300px 1fr; gap: 20px; height: 100%;">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-sitemap"></i>
                                <h3 class="card-title">组织架构</h3>
                            </div>
                            <div class="card-content">
                                <div style="padding: 10px;">
                                    <div style="padding: 8px; cursor: pointer; border-radius: 4px; margin-bottom: 5px; background: rgba(52, 152, 219, 0.1);">
                                        <i class="fas fa-building" style="color: #3498db; margin-right: 8px;"></i>指挥中心
                                    </div>
                                    <div style="padding: 8px 8px 8px 24px; cursor: pointer; border-radius: 4px; margin-bottom: 5px;">
                                        <i class="fas fa-users" style="color: #2ecc71; margin-right: 8px;"></i>调度部门
                                    </div>
                                    <div style="padding: 8px 8px 8px 24px; cursor: pointer; border-radius: 4px; margin-bottom: 5px;">
                                        <i class="fas fa-users" style="color: #2ecc71; margin-right: 8px;"></i>技术部门
                                    </div>
                                    <div style="padding: 8px 8px 8px 24px; cursor: pointer; border-radius: 4px; margin-bottom: 5px;">
                                        <i class="fas fa-users" style="color: #2ecc71; margin-right: 8px;"></i>运维部门
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-address-book"></i>
                                <h3 class="card-title">联系人列表</h3>
                                <div style="margin-left: auto;">
                                    <input type="text" placeholder="搜索..." style="padding: 5px; border: 1px solid #34495e; border-radius: 4px; background: #34495e; color: white;">
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="contacts-list" id="contactsList"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Map Panel -->
                <div class="panel" id="map" style="padding: 20px;">
                    <div style="display: grid; grid-template-columns: 300px 1fr; gap: 20px; height: 100%;">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-layer-group"></i>
                                <h3 class="card-title">图层控制</h3>
                            </div>
                            <div class="card-content">
                                <div style="padding: 10px;">
                                    <label style="display: flex; align-items: center; margin-bottom: 10px; cursor: pointer;">
                                        <input type="checkbox" checked style="margin-right: 8px;"> 设备图层
                                    </label>
                                    <label style="display: flex; align-items: center; margin-bottom: 10px; cursor: pointer;">
                                        <input type="checkbox" checked style="margin-right: 8px;"> 区域图层
                                    </label>
                                    <label style="display: flex; align-items: center; margin-bottom: 10px; cursor: pointer;">
                                        <input type="checkbox" style="margin-right: 8px;"> 路网图层
                                    </label>
                                    <label style="display: flex; align-items: center; margin-bottom: 10px; cursor: pointer;">
                                        <input type="checkbox" style="margin-right: 8px;"> 热力图层
                                    </label>
                                </div>
                                <hr style="border: 1px solid #34495e; margin: 15px 0;">
                                <div style="padding: 10px;">
                                    <h4 style="margin-bottom: 10px;">设备筛选</h4>
                                    <select style="width: 100%; padding: 5px; background: #34495e; color: white; border: 1px solid #3498db; border-radius: 4px;">
                                        <option>全部设备</option>
                                        <option>在线设备</option>
                                        <option>离线设备</option>
                                        <option>告警设备</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-map"></i>
                                <h3 class="card-title">GIS地图可视化</h3>
                            </div>
                            <div class="card-content">
                                <div id="fullMapContainer" style="width: 100%; height: 100%; border-radius: 8px;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Devices Panel -->
                <div class="panel" id="devices" style="padding: 20px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; grid-template-rows: 1fr 1fr; gap: 20px; height: 100%;">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-list"></i>
                                <h3 class="card-title">设备列表</h3>
                                <div style="margin-left: auto;">
                                    <button style="background: #27ae60; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; margin-left: 5px;">
                                        <i class="fas fa-plus"></i> 添加
                                    </button>
                                    <button style="background: #3498db; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; margin-left: 5px;">
                                        <i class="fas fa-sync"></i> 刷新
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div style="overflow-y: auto; height: 100%;" id="deviceManagementList"></div>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-cog"></i>
                                <h3 class="card-title">设备配置</h3>
                            </div>
                            <div class="card-content">
                                <div style="padding: 20px;">
                                    <div style="margin-bottom: 15px;">
                                        <label style="display: block; margin-bottom: 5px;">设备名称</label>
                                        <input type="text" value="移动终端001" style="width: 100%; padding: 8px; border: 1px solid #34495e; border-radius: 4px; background: #34495e; color: white;">
                                    </div>
                                    <div style="margin-bottom: 15px;">
                                        <label style="display: block; margin-bottom: 5px;">设备类型</label>
                                        <select style="width: 100%; padding: 8px; background: #34495e; color: white; border: 1px solid #34495e; border-radius: 4px;">
                                            <option>移动终端</option>
                                            <option>固定终端</option>
                                            <option>车载终端</option>
                                        </select>
                                    </div>
                                    <div style="margin-bottom: 15px;">
                                        <label style="display: block; margin-bottom: 5px;">工作模式</label>
                                        <select style="width: 100%; padding: 8px; background: #34495e; color: white; border: 1px solid #34495e; border-radius: 4px;">
                                            <option>常规模式</option>
                                            <option>应急模式</option>
                                            <option>静默模式</option>
                                        </select>
                                    </div>
                                    <div style="display: flex; gap: 10px; margin-top: 20px;">
                                        <button style="flex: 1; padding: 10px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">保存</button>
                                        <button style="flex: 1; padding: 10px; background: #95a5a6; color: white; border: none; border-radius: 4px; cursor: pointer;">重置</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-chart-line"></i>
                                <h3 class="card-title">设备状态监控</h3>
                            </div>
                            <div class="card-content">
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <div class="stat-number" style="color: #27ae60;">142</div>
                                        <div class="stat-label">在线设备</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number" style="color: #e74c3c;">14</div>
                                        <div class="stat-label">离线设备</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number" style="color: #f39c12;">8</div>
                                        <div class="stat-label">告警设备</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number" style="color: #3498db;">91%</div>
                                        <div class="stat-label">在线率</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-tasks"></i>
                                <h3 class="card-title">批量操作</h3>
                            </div>
                            <div class="card-content">
                                <div style="padding: 20px;">
                                    <div style="margin-bottom: 15px;">
                                        <label style="display: block; margin-bottom: 5px;">选择操作</label>
                                        <select style="width: 100%; padding: 8px; background: #34495e; color: white; border: 1px solid #34495e; border-radius: 4px;">
                                            <option>批量重启</option>
                                            <option>批量配置更新</option>
                                            <option>批量模式切换</option>
                                            <option>批量固件升级</option>
                                        </select>
                                    </div>
                                    <div style="margin-bottom: 15px;">
                                        <label style="display: block; margin-bottom: 5px;">目标设备</label>
                                        <textarea placeholder="输入设备ID，用逗号分隔" style="width: 100%; height: 60px; padding: 8px; border: 1px solid #34495e; border-radius: 4px; background: #34495e; color: white; resize: none;"></textarea>
                                    </div>
                                    <button style="width: 100%; padding: 10px; background: #e67e22; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                        <i class="fas fa-play"></i> 执行操作
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Schedule Panel -->
                <div class="panel" id="schedule" style="padding: 20px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; height: 100%;">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-calendar-alt"></i>
                                <h3 class="card-title">调度任务</h3>
                                <div style="margin-left: auto;">
                                    <button style="background: #27ae60; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer;">
                                        <i class="fas fa-plus"></i> 新建任务
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div style="overflow-y: auto; height: 100%;" id="scheduleList"></div>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-plus-circle"></i>
                                <h3 class="card-title">创建调度任务</h3>
                            </div>
                            <div class="card-content">
                                <div style="padding: 20px;">
                                    <div style="margin-bottom: 15px;">
                                        <label style="display: block; margin-bottom: 5px;">任务名称</label>
                                        <input type="text" placeholder="输入任务名称" style="width: 100%; padding: 8px; border: 1px solid #34495e; border-radius: 4px; background: #34495e; color: white;">
                                    </div>
                                    <div style="margin-bottom: 15px;">
                                        <label style="display: block; margin-bottom: 5px;">任务类型</label>
                                        <select style="width: 100%; padding: 8px; background: #34495e; color: white; border: 1px solid #34495e; border-radius: 4px;">
                                            <option>紧急调度</option>
                                            <option>例行巡检</option>
                                            <option>设备维护</option>
                                            <option>会议安排</option>
                                        </select>
                                    </div>
                                    <div style="margin-bottom: 15px;">
                                        <label style="display: block; margin-bottom: 5px;">执行时间</label>
                                        <input type="datetime-local" style="width: 100%; padding: 8px; border: 1px solid #34495e; border-radius: 4px; background: #34495e; color: white;">
                                    </div>
                                    <div style="margin-bottom: 15px;">
                                        <label style="display: block; margin-bottom: 5px;">参与人员</label>
                                        <textarea placeholder="选择参与人员" style="width: 100%; height: 60px; padding: 8px; border: 1px solid #34495e; border-radius: 4px; background: #34495e; color: white; resize: none;"></textarea>
                                    </div>
                                    <div style="margin-bottom: 15px;">
                                        <label style="display: block; margin-bottom: 5px;">任务描述</label>
                                        <textarea placeholder="输入任务详细描述" style="width: 100%; height: 80px; padding: 8px; border: 1px solid #34495e; border-radius: 4px; background: #34495e; color: white; resize: none;"></textarea>
                                    </div>
                                    <button style="width: 100%; padding: 10px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                        <i class="fas fa-save"></i> 创建任务
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Command Panel -->
                <div class="panel" id="command" style="padding: 20px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; grid-template-rows: 1fr 1fr; gap: 20px; height: 100%;">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-exclamation-triangle"></i>
                                <h3 class="card-title">应急指挥</h3>
                            </div>
                            <div class="card-content">
                                <div style="padding: 20px; text-align: center;">
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                        <button style="padding: 20px; background: #e74c3c; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;">
                                            <i class="fas fa-fire" style="display: block; font-size: 24px; margin-bottom: 8px;"></i>
                                            火灾应急
                                        </button>
                                        <button style="padding: 20px; background: #f39c12; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;">
                                            <i class="fas fa-exclamation" style="display: block; font-size: 24px; margin-bottom: 8px;"></i>
                                            安全事故
                                        </button>
                                        <button style="padding: 20px; background: #9b59b6; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;">
                                            <i class="fas fa-medkit" style="display: block; font-size: 24px; margin-bottom: 8px;"></i>
                                            医疗急救
                                        </button>
                                        <button style="padding: 20px; background: #34495e; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;">
                                            <i class="fas fa-cog" style="display: block; font-size: 24px; margin-bottom: 8px;"></i>
                                            设备故障
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-broadcast-tower"></i>
                                <h3 class="card-title">一键调度</h3>
                            </div>
                            <div class="card-content">
                                <div style="padding: 20px;">
                                    <div style="margin-bottom: 15px;">
                                        <label style="display: block; margin-bottom: 5px;">调度类型</label>
                                        <select style="width: 100%; padding: 8px; background: #34495e; color: white; border: 1px solid #34495e; border-radius: 4px;">
                                            <option>全员集合</option>
                                            <option>部门集合</option>
                                            <option>紧急疏散</option>
                                            <option>设备检查</option>
                                        </select>
                                    </div>
                                    <div style="margin-bottom: 15px;">
                                        <label style="display: block; margin-bottom: 5px;">紧急程度</label>
                                        <select style="width: 100%; padding: 8px; background: #34495e; color: white; border: 1px solid #34495e; border-radius: 4px;">
                                            <option style="color: #e74c3c;">特急</option>
                                            <option style="color: #f39c12;">紧急</option>
                                            <option style="color: #3498db;">一般</option>
                                        </select>
                                    </div>
                                    <div style="margin-bottom: 20px;">
                                        <label style="display: block; margin-bottom: 5px;">调度消息</label>
                                        <textarea placeholder="输入调度指令内容" style="width: 100%; height: 80px; padding: 8px; border: 1px solid #34495e; border-radius: 4px; background: #34495e; color: white; resize: none;"></textarea>
                                    </div>
                                    <button style="width: 100%; padding: 15px; background: #e74c3c; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; font-weight: bold;">
                                        <i class="fas fa-bullhorn"></i> 立即执行调度
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-history"></i>
                                <h3 class="card-title">指挥记录</h3>
                            </div>
                            <div class="card-content">
                                <div style="overflow-y: auto; height: 100%;" id="commandHistory"></div>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-header">
                                <i class="fas fa-users-cog"></i>
                                <h3 class="card-title">人员状态</h3>
                            </div>
                            <div class="card-content">
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <div class="stat-number" style="color: #27ae60;">89</div>
                                        <div class="stat-label">待命人员</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number" style="color: #3498db;">23</div>
                                        <div class="stat-label">执行任务</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number" style="color: #f39c12;">12</div>
                                        <div class="stat-label">休假人员</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number" style="color: #e74c3c;">3</div>
                                        <div class="stat-label">紧急状态</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            // Update time
            updateTime();
            setInterval(updateTime, 1000);
            
            // Initialize navigation
            initializeNavigation();
            
            // Initialize map
            initializeMap();
            
            // Load mock data
            loadMockData();
        }

        function updateTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleTimeString('zh-CN');
        }

        function initializeNavigation() {
            const navItems = document.querySelectorAll('.nav-item');
            const panels = document.querySelectorAll('.panel');
            
            navItems.forEach(item => {
                item.addEventListener('click', () => {
                    const panelId = item.dataset.panel;
                    
                    // Update active nav item
                    navItems.forEach(nav => nav.classList.remove('active'));
                    item.classList.add('active');
                    
                    // Update active panel
                    panels.forEach(panel => panel.classList.remove('active'));
                    const targetPanel = document.getElementById(panelId);
                    if (targetPanel) {
                        targetPanel.classList.add('active');
                    }
                    
                    // Update header title
                    const title = item.querySelector('span').textContent;
                    document.querySelector('.header-title').textContent = title;
                });
            });
        }

        function initializeMap() {
            // Initialize Amap
            const map = new AMap.Map('mapContainer', {
                zoom: 10,
                center: [116.397428, 39.90923],
                mapStyle: 'amap://styles/dark'
            });
            
            // Add device markers
            const devices = [
                { name: '设备001', position: [116.397428, 39.90923], status: 'online' },
                { name: '设备002', position: [116.407428, 39.91923], status: 'online' },
                { name: '设备003', position: [116.387428, 39.89923], status: 'offline' },
                { name: '设备004', position: [116.417428, 39.92923], status: 'online' },
                { name: '设备005', position: [116.377428, 39.88923], status: 'online' }
            ];
            
            devices.forEach(device => {
                const marker = new AMap.Marker({
                    position: device.position,
                    title: device.name,
                    icon: new AMap.Icon({
                        size: new AMap.Size(20, 20),
                        image: device.status === 'online' ? 
                            'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTAiIGN5PSIxMCIgcj0iMTAiIGZpbGw9IiMyN2FlNjAiLz4KPHN2Zz4K' :
                            'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTAiIGN5PSIxMCIgcj0iMTAiIGZpbGw9IiNlNzRjM2MiLz4KPHN2Zz4K'
                    })
                });
                
                map.add(marker);
                
                // Add click event for device info
                marker.on('click', () => {
                    showDeviceInfo(device);
                });
            });
        }

        function showDeviceInfo(device) {
            alert(`设备信息:\n名称: ${device.name}\n状态: ${device.status === 'online' ? '在线' : '离线'}\n位置: ${device.position.join(', ')}`);
        }

        function loadMockData() {
            // Load device list
            const deviceList = document.getElementById('deviceList');
            const devices = [
                { name: '移动终端001', status: 'online', signal: '强' },
                { name: '移动终端002', status: 'online', signal: '中' },
                { name: '移动终端003', status: 'offline', signal: '无' },
                { name: '移动终端004', status: 'online', signal: '强' },
                { name: '移动终端005', status: 'online', signal: '弱' }
            ];

            deviceList.innerHTML = devices.map(device => `
                <div class="device-item ${device.status}">
                    <div class="device-status"></div>
                    <div>
                        <div>${device.name}</div>
                        <small style="color: #bdc3c7;">信号: ${device.signal}</small>
                    </div>
                </div>
            `).join('');

            // Load participants list
            const participantsList = document.getElementById('participantsList');
            const participants = [
                { name: '张三', role: '指挥员', status: 'speaking' },
                { name: '李四', role: '操作员', status: 'muted' },
                { name: '王五', role: '技术员', status: 'online' },
                { name: '赵六', role: '调度员', status: 'online' }
            ];

            participantsList.innerHTML = participants.map(participant => `
                <div class="contact-item">
                    <div class="contact-avatar">${participant.name.charAt(0)}</div>
                    <div class="contact-info">
                        <h4>${participant.name}</h4>
                        <p>${participant.role} - ${participant.status === 'speaking' ? '发言中' : participant.status === 'muted' ? '静音' : '在线'}</p>
                    </div>
                </div>
            `).join('');

            // Load chat messages
            const chatMessages = document.getElementById('chatMessages');
            const messages = [
                { sender: '张三', message: '大家好，会议开始', time: '14:30' },
                { sender: '李四', message: '收到，准备就绪', time: '14:31' },
                { sender: '王五', message: '设备检查完毕', time: '14:32' }
            ];

            chatMessages.innerHTML = messages.map(msg => `
                <div style="margin-bottom: 15px; padding: 10px; background: rgba(52, 152, 219, 0.1); border-radius: 8px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <strong>${msg.sender}</strong>
                        <small style="color: #bdc3c7;">${msg.time}</small>
                    </div>
                    <div>${msg.message}</div>
                </div>
            `).join('');

            // Load messaging contacts
            loadMessagingData();

            // Load call history
            loadCallHistory();

            // Load files
            loadFilesData();

            // Load contacts
            loadContactsData();

            // Load device management
            loadDeviceManagement();

            // Load schedule
            loadScheduleData();

            // Load command history
            loadCommandHistory();

            // Initialize full map
            initializeFullMap();
        }

        function loadMessagingData() {
            const messagingContacts = document.getElementById('messagingContacts');
            const contacts = [
                { name: '张三', status: 'online', lastMessage: '会议准备完毕', time: '14:35' },
                { name: '李四', status: 'online', lastMessage: '设备检查中...', time: '14:30' },
                { name: '王五', status: 'away', lastMessage: '稍后联系', time: '14:25' },
                { name: '赵六', status: 'offline', lastMessage: '明天见', time: '昨天' }
            ];

            messagingContacts.innerHTML = contacts.map(contact => `
                <div class="contact-item">
                    <div class="contact-avatar">${contact.name.charAt(0)}</div>
                    <div class="contact-info">
                        <h4>${contact.name} <span style="color: ${contact.status === 'online' ? '#27ae60' : contact.status === 'away' ? '#f39c12' : '#e74c3c'};">●</span></h4>
                        <p style="font-size: 11px;">${contact.lastMessage}</p>
                        <small style="color: #bdc3c7; font-size: 10px;">${contact.time}</small>
                    </div>
                </div>
            `).join('');

            const messagingChat = document.getElementById('messagingChat');
            const chatHistory = [
                { sender: '张三', message: '今天的会议准备得怎么样了？', time: '14:30', isMe: false },
                { sender: '我', message: '已经准备完毕，设备都检查过了', time: '14:32', isMe: true },
                { sender: '张三', message: '很好，那我们准时开始', time: '14:35', isMe: false }
            ];

            messagingChat.innerHTML = chatHistory.map(msg => `
                <div style="margin-bottom: 15px; display: flex; ${msg.isMe ? 'justify-content: flex-end' : 'justify-content: flex-start'};">
                    <div style="max-width: 70%; padding: 10px; border-radius: 8px; background: ${msg.isMe ? '#3498db' : 'rgba(52, 152, 219, 0.1)'};">
                        <div style="font-size: 12px; color: ${msg.isMe ? '#ecf0f1' : '#bdc3c7'}; margin-bottom: 5px;">${msg.sender} ${msg.time}</div>
                        <div>${msg.message}</div>
                    </div>
                </div>
            `).join('');
        }

        function loadCallHistory() {
            const callHistory = document.getElementById('callHistory');
            const calls = [
                { name: '张三', type: 'incoming', duration: '00:05:23', time: '14:30', status: 'completed' },
                { name: '李四', type: 'outgoing', duration: '00:02:15', time: '14:15', status: 'completed' },
                { name: '王五', type: 'missed', duration: '00:00:00', time: '14:00', status: 'missed' },
                { name: '赵六', type: 'incoming', duration: '00:08:45', time: '13:45', status: 'completed' }
            ];

            callHistory.innerHTML = calls.map(call => `
                <div class="device-item">
                    <div style="margin-right: 10px;">
                        <i class="fas fa-phone${call.type === 'incoming' ? '-alt' : call.type === 'outgoing' ? '' : '-slash'}"
                           style="color: ${call.status === 'completed' ? '#27ae60' : '#e74c3c'};"></i>
                    </div>
                    <div style="flex: 1;">
                        <div>${call.name}</div>
                        <small style="color: #bdc3c7;">${call.duration} - ${call.time}</small>
                    </div>
                    <button style="background: #3498db; color: white; border: none; padding: 5px 8px; border-radius: 4px; cursor: pointer;">
                        <i class="fas fa-phone"></i>
                    </button>
                </div>
            `).join('');
        }

        function loadFilesData() {
            const filesList = document.getElementById('filesList');
            const files = [
                { name: '会议纪要.docx', size: '2.3MB', type: 'document', modified: '2小时前' },
                { name: '系统架构图.png', size: '1.8MB', type: 'image', modified: '1天前' },
                { name: '培训视频.mp4', size: '156MB', type: 'video', modified: '3天前' },
                { name: '设备清单.xlsx', size: '856KB', type: 'spreadsheet', modified: '1周前' },
                { name: '操作手册.pdf', size: '4.2MB', type: 'pdf', modified: '2周前' },
                { name: '备份文件.zip', size: '89MB', type: 'archive', modified: '1个月前' }
            ];

            const getFileIcon = (type) => {
                const icons = {
                    document: 'fa-file-word',
                    image: 'fa-file-image',
                    video: 'fa-file-video',
                    spreadsheet: 'fa-file-excel',
                    pdf: 'fa-file-pdf',
                    archive: 'fa-file-archive'
                };
                return icons[type] || 'fa-file';
            };

            filesList.innerHTML = files.map(file => `
                <div style="padding: 15px; background: rgba(52, 152, 219, 0.1); border-radius: 8px; text-align: center; cursor: pointer; transition: background 0.3s;"
                     onmouseover="this.style.background='rgba(52, 152, 219, 0.2)'"
                     onmouseout="this.style.background='rgba(52, 152, 219, 0.1)'">
                    <i class="fas ${getFileIcon(file.type)}" style="font-size: 32px; color: #3498db; margin-bottom: 10px;"></i>
                    <div style="font-size: 12px; margin-bottom: 5px; word-break: break-all;">${file.name}</div>
                    <div style="font-size: 10px; color: #bdc3c7;">${file.size}</div>
                    <div style="font-size: 10px; color: #bdc3c7;">${file.modified}</div>
                </div>
            `).join('');
        }

        function loadContactsData() {
            const contactsList = document.getElementById('contactsList');
            const contacts = [
                { name: '张三', role: '指挥中心主任', phone: '138****1234', email: '<EMAIL>', department: '指挥中心', status: 'online' },
                { name: '李四', role: '调度部门经理', phone: '139****5678', email: '<EMAIL>', department: '调度部门', status: 'online' },
                { name: '王五', role: '技术部门工程师', phone: '137****9012', email: '<EMAIL>', department: '技术部门', status: 'away' },
                { name: '赵六', role: '运维部门主管', phone: '136****3456', email: '<EMAIL>', department: '运维部门', status: 'offline' },
                { name: '钱七', role: '系统管理员', phone: '135****7890', email: '<EMAIL>', department: '技术部门', status: 'online' }
            ];

            contactsList.innerHTML = contacts.map(contact => `
                <div class="contact-item">
                    <div class="contact-avatar">${contact.name.charAt(0)}</div>
                    <div class="contact-info" style="flex: 1;">
                        <h4>${contact.name} <span style="color: ${contact.status === 'online' ? '#27ae60' : contact.status === 'away' ? '#f39c12' : '#e74c3c'};">●</span></h4>
                        <p>${contact.role}</p>
                        <p style="font-size: 11px; color: #bdc3c7;">${contact.phone} | ${contact.department}</p>
                    </div>
                    <div style="display: flex; gap: 5px;">
                        <button style="background: #27ae60; color: white; border: none; padding: 5px 8px; border-radius: 4px; cursor: pointer;" title="语音通话">
                            <i class="fas fa-phone"></i>
                        </button>
                        <button style="background: #3498db; color: white; border: none; padding: 5px 8px; border-radius: 4px; cursor: pointer;" title="视频通话">
                            <i class="fas fa-video"></i>
                        </button>
                        <button style="background: #f39c12; color: white; border: none; padding: 5px 8px; border-radius: 4px; cursor: pointer;" title="发送消息">
                            <i class="fas fa-comment"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function loadDeviceManagement() {
            const deviceManagementList = document.getElementById('deviceManagementList');
            const devices = [
                { id: 'DEV001', name: '移动终端001', type: '移动终端', status: 'online', mode: '常规模式', signal: '强', battery: '85%', location: '北京市朝阳区' },
                { id: 'DEV002', name: '移动终端002', type: '移动终端', status: 'online', mode: '应急模式', signal: '中', battery: '67%', location: '北京市海淀区' },
                { id: 'DEV003', name: '固定终端001', type: '固定终端', status: 'offline', mode: '静默模式', signal: '无', battery: 'N/A', location: '北京市西城区' },
                { id: 'DEV004', name: '车载终端001', type: '车载终端', status: 'online', mode: '常规模式', signal: '强', battery: '92%', location: '北京市东城区' },
                { id: 'DEV005', name: '移动终端003', type: '移动终端', status: 'online', mode: '常规模式', signal: '弱', battery: '23%', location: '北京市丰台区' }
            ];

            deviceManagementList.innerHTML = devices.map(device => `
                <div style="padding: 15px; margin-bottom: 10px; background: rgba(52, 152, 219, 0.1); border-radius: 8px; border-left: 3px solid ${device.status === 'online' ? '#27ae60' : '#e74c3c'};">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <h4>${device.name}</h4>
                        <div style="display: flex; gap: 5px;">
                            <button style="background: #3498db; color: white; border: none; padding: 3px 6px; border-radius: 3px; cursor: pointer; font-size: 12px;" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button style="background: #e74c3c; color: white; border: none; padding: 3px 6px; border-radius: 3px; cursor: pointer; font-size: 12px;" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 5px; font-size: 12px; color: #bdc3c7;">
                        <div>ID: ${device.id}</div>
                        <div>类型: ${device.type}</div>
                        <div>状态: <span style="color: ${device.status === 'online' ? '#27ae60' : '#e74c3c'};">${device.status === 'online' ? '在线' : '离线'}</span></div>
                        <div>模式: ${device.mode}</div>
                        <div>信号: ${device.signal}</div>
                        <div>电量: ${device.battery}</div>
                    </div>
                    <div style="font-size: 11px; color: #95a5a6; margin-top: 5px;">
                        <i class="fas fa-map-marker-alt"></i> ${device.location}
                    </div>
                </div>
            `).join('');
        }

        function loadScheduleData() {
            const scheduleList = document.getElementById('scheduleList');
            const schedules = [
                { id: 'SCH001', name: '紧急演练', type: '紧急调度', time: '2024-01-15 14:00', participants: '全体人员', status: 'pending', priority: 'high' },
                { id: 'SCH002', name: '设备巡检', type: '例行巡检', time: '2024-01-16 09:00', participants: '技术部门', status: 'in-progress', priority: 'medium' },
                { id: 'SCH003', name: '系统维护', type: '设备维护', time: '2024-01-17 02:00', participants: '运维团队', status: 'scheduled', priority: 'low' },
                { id: 'SCH004', name: '月度会议', type: '会议安排', time: '2024-01-18 10:00', participants: '管理层', status: 'completed', priority: 'medium' },
                { id: 'SCH005', name: '应急响应测试', type: '紧急调度', time: '2024-01-19 16:00', participants: '应急小组', status: 'pending', priority: 'high' }
            ];

            const getStatusColor = (status) => {
                const colors = {
                    'pending': '#f39c12',
                    'in-progress': '#3498db',
                    'scheduled': '#95a5a6',
                    'completed': '#27ae60'
                };
                return colors[status] || '#95a5a6';
            };

            const getStatusText = (status) => {
                const texts = {
                    'pending': '待执行',
                    'in-progress': '进行中',
                    'scheduled': '已安排',
                    'completed': '已完成'
                };
                return texts[status] || '未知';
            };

            scheduleList.innerHTML = schedules.map(schedule => `
                <div style="padding: 15px; margin-bottom: 10px; background: rgba(52, 152, 219, 0.1); border-radius: 8px; border-left: 3px solid ${schedule.priority === 'high' ? '#e74c3c' : schedule.priority === 'medium' ? '#f39c12' : '#27ae60'};">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <h4>${schedule.name}</h4>
                        <span style="background: ${getStatusColor(schedule.status)}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px;">
                            ${getStatusText(schedule.status)}
                        </span>
                    </div>
                    <div style="font-size: 12px; color: #bdc3c7; margin-bottom: 5px;">
                        <i class="fas fa-clock"></i> ${schedule.time}
                    </div>
                    <div style="font-size: 12px; color: #bdc3c7; margin-bottom: 5px;">
                        <i class="fas fa-tag"></i> ${schedule.type}
                    </div>
                    <div style="font-size: 12px; color: #bdc3c7;">
                        <i class="fas fa-users"></i> ${schedule.participants}
                    </div>
                    <div style="display: flex; gap: 5px; margin-top: 10px;">
                        <button style="background: #3498db; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 11px;">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button style="background: #27ae60; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 11px;">
                            <i class="fas fa-play"></i> 执行
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function loadCommandHistory() {
            const commandHistory = document.getElementById('commandHistory');
            const commands = [
                { id: 'CMD001', type: '全员集合', level: '特急', time: '2024-01-15 14:30', operator: '张三', status: '已执行', participants: 156 },
                { id: 'CMD002', type: '设备检查', level: '一般', time: '2024-01-15 10:15', operator: '李四', status: '进行中', participants: 23 },
                { id: 'CMD003', type: '紧急疏散', level: '紧急', time: '2024-01-14 16:45', operator: '王五', status: '已完成', participants: 89 },
                { id: 'CMD004', type: '部门集合', level: '一般', time: '2024-01-14 09:30', operator: '赵六', status: '已完成', participants: 34 }
            ];

            const getLevelColor = (level) => {
                const colors = {
                    '特急': '#e74c3c',
                    '紧急': '#f39c12',
                    '一般': '#3498db'
                };
                return colors[level] || '#95a5a6';
            };

            commandHistory.innerHTML = commands.map(command => `
                <div style="padding: 12px; margin-bottom: 8px; background: rgba(52, 152, 219, 0.1); border-radius: 6px; border-left: 3px solid ${getLevelColor(command.level)};">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                        <h5 style="margin: 0;">${command.type}</h5>
                        <span style="background: ${getLevelColor(command.level)}; color: white; padding: 1px 6px; border-radius: 8px; font-size: 10px;">
                            ${command.level}
                        </span>
                    </div>
                    <div style="font-size: 11px; color: #bdc3c7; margin-bottom: 3px;">
                        <i class="fas fa-clock"></i> ${command.time}
                    </div>
                    <div style="font-size: 11px; color: #bdc3c7; margin-bottom: 3px;">
                        <i class="fas fa-user"></i> 操作员: ${command.operator}
                    </div>
                    <div style="font-size: 11px; color: #bdc3c7;">
                        <i class="fas fa-users"></i> 参与人数: ${command.participants}
                    </div>
                </div>
            `).join('');
        }

        function initializeFullMap() {
            // Initialize full-screen map for GIS panel
            setTimeout(() => {
                const fullMapContainer = document.getElementById('fullMapContainer');
                if (fullMapContainer) {
                    const fullMap = new AMap.Map('fullMapContainer', {
                        zoom: 12,
                        center: [116.397428, 39.90923],
                        mapStyle: 'amap://styles/dark'
                    });

                    // Add more detailed device markers
                    const detailedDevices = [
                        { name: '移动终端001', position: [116.397428, 39.90923], status: 'online', type: 'mobile' },
                        { name: '移动终端002', position: [116.407428, 39.91923], status: 'online', type: 'mobile' },
                        { name: '固定终端001', position: [116.387428, 39.89923], status: 'offline', type: 'fixed' },
                        { name: '车载终端001', position: [116.417428, 39.92923], status: 'online', type: 'vehicle' },
                        { name: '移动终端003', position: [116.377428, 39.88923], status: 'online', type: 'mobile' },
                        { name: '固定终端002', position: [116.427428, 39.93923], status: 'online', type: 'fixed' },
                        { name: '车载终端002', position: [116.367428, 39.87923], status: 'online', type: 'vehicle' }
                    ];

                    detailedDevices.forEach(device => {
                        const iconColor = device.status === 'online' ? '#27ae60' : '#e74c3c';
                        const iconShape = device.type === 'mobile' ? 'circle' : device.type === 'vehicle' ? 'square' : 'triangle';

                        const marker = new AMap.Marker({
                            position: device.position,
                            title: device.name,
                            icon: new AMap.Icon({
                                size: new AMap.Size(24, 24),
                                image: `data:image/svg+xml;base64,${btoa(`
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="12" fill="${iconColor}"/>
                                        <text x="12" y="16" text-anchor="middle" fill="white" font-size="10" font-weight="bold">
                                            ${device.type === 'mobile' ? 'M' : device.type === 'vehicle' ? 'V' : 'F'}
                                        </text>
                                    </svg>
                                `)}`
                            })
                        });

                        fullMap.add(marker);

                        // Add info window
                        const infoWindow = new AMap.InfoWindow({
                            content: `
                                <div style="padding: 10px; color: #333;">
                                    <h4 style="margin: 0 0 8px 0;">${device.name}</h4>
                                    <p style="margin: 0; font-size: 12px;">状态: <span style="color: ${iconColor};">${device.status === 'online' ? '在线' : '离线'}</span></p>
                                    <p style="margin: 0; font-size: 12px;">类型: ${device.type === 'mobile' ? '移动终端' : device.type === 'vehicle' ? '车载终端' : '固定终端'}</p>
                                    <p style="margin: 0; font-size: 12px;">位置: ${device.position.join(', ')}</p>
                                </div>
                            `
                        });

                        marker.on('click', () => {
                            infoWindow.open(fullMap, device.position);
                        });
                    });
                }
            }, 100);
        }
    </script>
</body>
</html>
